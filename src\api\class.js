import request from './axios';

/**
 * 获取班级列表
 * @param {Object} params 查询参数
 * @param {string} params.createBy 创建者
 * @param {string} params.createTime 创建时间
 * @param {string} params.updateBy 更新者
 * @param {string} params.updateTime 更新时间
 * @param {string} params.remark 备注
 * @param {Object} params.params 其他参数
 * @param {number} params.id 班级ID
 * @param {string} params.name 班级名
 * @returns {Promise<Object>} 班级列表响应数据
 */
export const getClassesList = (params = {}) => {
  console.log('正在获取班级列表...', params);

  // 构建查询参数，过滤掉空值
  const cleanParams = {};
  Object.keys(params).forEach(key => {
    if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
      cleanParams[key] = params[key];
    }
  });

  return request({
    url: '/core/classes/list',
    method: 'get',
    params: cleanParams,
    // 使用表单编码格式，与接口文档保持一致
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  }).then(response => {
    console.log('获取班级列表成功:', response);

    // 检查响应格式是否符合预期的TableDataInfo结构
    if (response && typeof response === 'object') {
      // 标准响应格式：{ total, rows, code, msg }
      if (response.hasOwnProperty('total') && response.hasOwnProperty('rows')) {
        console.log('返回标准TableDataInfo格式');
        return response;
      }
      // 如果直接返回数组，包装成标准格式
      else if (Array.isArray(response)) {
        console.log('返回数组格式，包装成标准格式');
        return {
          total: response.length,
          rows: response,
          code: 0,
          msg: '成功'
        };
      }
      // 如果有data字段且是数组，提取数据
      else if (response.data && Array.isArray(response.data)) {
        console.log('从嵌套响应中提取班级列表数据');
        return {
          total: response.data.length,
          rows: response.data,
          code: response.code || 0,
          msg: response.msg || '成功'
        };
      }
    }

    console.warn('响应格式不符合预期，返回空结果');
    return {
      total: 0,
      rows: [],
      code: -1,
      msg: '响应格式不符合预期'
    };
  }).catch(error => {
    console.error('获取班级列表失败:', error);
    // 返回错误格式，保持与TableDataInfo一致
    return {
      total: 0,
      rows: [],
      code: -1,
      msg: error.message || '获取班级列表失败'
    };
  });
};

/**
 * 创建新班级
 * @param {Object} classData 班级数据
 * @param {string} classData.createBy 创建者
 * @param {string} classData.createTime 创建时间
 * @param {string} classData.updateBy 更新者
 * @param {string} classData.updateTime 更新时间
 * @param {string} classData.remark 备注
 * @param {Object} classData.params 其他参数
 * @param {number} classData.id 班级ID
 * @param {string} classData.name 班级名称
 * @returns {Promise<Object>} 创建结果
 */
export const createClass = (classData) => {
  console.log('正在创建班级...', classData);

  return request({
    url: '/core/classes',
    method: 'post',
    data: classData,
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(response => {
    console.log('创建班级成功:', response);

    // 检查响应格式
    if (response && typeof response === 'object') {
      // 标准响应格式：{ error, success, warn, empty }
      if (response.hasOwnProperty('success')) {
        console.log('返回标准AjaxResult格式');
        return {
          success: response.success,
          error: response.error || false,
          warn: response.warn || false,
          empty: response.empty || false,
          msg: response.msg || (response.success ? '创建成功' : '创建失败'),
          data: response.data || null
        };
      }
    }

    console.warn('响应格式不符合预期，返回默认成功结果');
    return {
      success: true,
      error: false,
      warn: false,
      empty: false,
      msg: '创建成功',
      data: response
    };
  }).catch(error => {
    console.error('创建班级失败:', error);
    // 返回错误格式，保持与AjaxResult一致
    return {
      success: false,
      error: true,
      warn: false,
      empty: false,
      msg: error.message || '创建班级失败',
      data: null
    };
  });
};

/**
 * 根据ID获取班级详情
 * @param {string|number} classId 班级ID
 * @returns {Promise<Object>} 班级详情数据
 */
export const getClassById = (classId) => {
  console.log(`正在获取班级详情: ${classId}`);

  return request({
    url: `/core/classes/${classId}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  }).then(response => {
    console.log(`获取班级${classId}详情成功:`, response);

    // 检查响应格式
    if (response && typeof response === 'object') {
      // 标准响应格式：{ error, success, warn, empty }
      if (response.hasOwnProperty('success')) {
        console.log('返回标准AjaxResult格式');
        return {
          success: response.success,
          error: response.error || false,
          warn: response.warn || false,
          empty: response.empty || false,
          msg: response.msg || (response.success ? '获取成功' : '获取失败'),
          data: response.data || null
        };
      }
    }

    console.warn('响应格式不符合预期，返回默认成功结果');
    return {
      success: true,
      error: false,
      warn: false,
      empty: false,
      msg: '获取成功',
      data: response
    };
  }).catch(error => {
    console.error(`获取班级 ${classId} 详情失败:`, error);
    return {
      success: false,
      error: true,
      warn: false,
      empty: false,
      msg: error.message || '获取班级详情失败',
      data: null
    };
  });
};

/**
 * 更新班级信息
 * @param {Object} classData 班级数据
 * @param {string} classData.createBy 创建者
 * @param {string} classData.createTime 创建时间
 * @param {string} classData.updateBy 更新者
 * @param {string} classData.updateTime 更新时间
 * @param {string} classData.remark 备注
 * @param {Object} classData.params 其他参数
 * @param {number} classData.id 班级ID
 * @param {string} classData.name 班级名称
 * @returns {Promise<Object>} 更新结果
 */
export const updateClass = (classData) => {
  console.log('正在更新班级...', classData);

  return request({
    url: '/core/classes',
    method: 'put',
    data: classData,
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(response => {
    console.log('更新班级成功:', response);

    // 检查响应格式
    if (response && typeof response === 'object') {
      // 标准响应格式：{ error, success, warn, empty }
      if (response.hasOwnProperty('success')) {
        console.log('返回标准AjaxResult格式');
        return {
          success: response.success,
          error: response.error || false,
          warn: response.warn || false,
          empty: response.empty || false,
          msg: response.msg || (response.success ? '更新成功' : '更新失败'),
          data: response.data || null
        };
      }
    }

    console.warn('响应格式不符合预期，返回默认成功结果');
    return {
      success: true,
      error: false,
      warn: false,
      empty: false,
      msg: '更新成功',
      data: response
    };
  }).catch(error => {
    console.error('更新班级失败:', error);
    // 返回错误格式，保持与AjaxResult一致
    return {
      success: false,
      error: true,
      warn: false,
      empty: false,
      msg: error.message || '更新班级失败',
      data: null
    };
  });
};

/**
 * 删除班级
 * @param {Array<number>} ids 要删除的班级ID数组
 * @returns {Promise<Object>} 删除结果
 */
export const deleteClasses = (ids) => {
  console.log('正在删除班级...', ids);

  // 验证参数
  if (!Array.isArray(ids) || ids.length === 0) {
    console.error('删除班级失败: 班级ID数组不能为空');
    return Promise.resolve({
      success: false,
      error: true,
      warn: false,
      empty: false,
      msg: '班级ID数组不能为空',
      data: null
    });
  }

  // 将数组转换为逗号分隔的字符串，符合路径参数格式
  const idsString = ids.join(',');

  return request({
    url: `/core/classes/${idsString}`,
    method: 'delete',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  }).then(response => {
    console.log('删除班级成功:', response);

    // 检查响应格式
    if (response && typeof response === 'object') {
      // 标准响应格式：{ error, success, warn, empty }
      if (response.hasOwnProperty('success')) {
        console.log('返回标准AjaxResult格式');
        return {
          success: response.success,
          error: response.error || false,
          warn: response.warn || false,
          empty: response.empty || false,
          msg: response.msg || (response.success ? '删除成功' : '删除失败'),
          data: response.data || null
        };
      }
    }

    console.warn('响应格式不符合预期，返回默认成功结果');
    return {
      success: true,
      error: false,
      warn: false,
      empty: false,
      msg: '删除成功',
      data: response
    };
  }).catch(error => {
    console.error('删除班级失败:', error);
    // 返回错误格式，保持与AjaxResult一致
    return {
      success: false,
      error: true,
      warn: false,
      empty: false,
      msg: error.message || '删除班级失败',
      data: null
    };
  });
};