<template>
    <div class="my-page">
      <div class="container py-3 full-width">
        <div class="page-header">
          <router-link to="/" class="back-to-home-btn">
            <i class="back-icon"></i>
            <span>返回首页</span>
          </router-link>
          <h1 class="page-title">AI智能OBE智慧教学平台</h1>
          <div class="header-subtitle">教师工作台</div>
        </div>
        
        <!-- 左侧菜单和主内容区 -->
        <div class="content-layout">
          <!-- 左侧菜单 -->
          <div class="sidebar">
            <div class="sidebar-menu">
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'knowledge-graph' }" 
                @click="activeTab = 'knowledge-graph'"
              >
                <i class="menu-icon knowledge-graph-icon"></i>
                <span class="menu-text">知识图谱</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'courses' }" 
                @click="activeTab = 'courses'"
              >
                <i class="menu-icon courses-icon"></i>
                <span class="menu-text">课程</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'lesson-assistant' }" 
                @click="activeTab = 'lesson-assistant'"
              >
                <i class="menu-icon lesson-assistant-icon"></i>
                <span class="menu-text">教学助手</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'student-management' }" 
                @click="activeTab = 'student-management'"
              >
                <i class="menu-icon student-management-icon"></i>
                <span class="menu-text">班级管理</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'homework-discussion' }" 
                @click="activeTab = 'homework-discussion'"
              >
                <i class="menu-icon homework-discussion-icon"></i>
                <span class="menu-text">作业与讨论</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'ai-assistant' }" 
                @click="activeTab = 'ai-assistant'"
              >
                <i class="menu-icon ai-assistant-icon"></i>
                <span class="menu-text">AI助手</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'materials' }" 
                @click="activeTab = 'materials'"
              >
                <i class="menu-icon materials-icon"></i>
                <span class="menu-text">资料库</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'settings' }" 
                @click="activeTab = 'settings'"
              >
                <i class="menu-icon settings-icon"></i>
                <span class="menu-text">设置</span>
              </div>
            </div>
          </div>
          
          <!-- 主内容区域 -->
          <div class="main-content">
            <!-- 知识图谱面板 -->
            <div v-if="activeTab === 'knowledge-graph'" class="knowledge-graph-panel">
              <!-- 子标签导航 -->
              <div class="sub-tabs">
                <div 
                  class="sub-tab-item" 
                  :class="{ active: subActiveTab === 'outline' }" 
                  @click="subActiveTab = 'outline'"
                >
                  <i class="sub-tab-icon outline-icon"></i>
                  <span>知识大纲</span>
                </div>
                <div 
                  class="sub-tab-item" 
                  :class="{ active: subActiveTab === 'graphview' }" 
                  @click="subActiveTab = 'graphview'"
                >
                  <i class="sub-tab-icon list-icon"></i>
                  <span>图谱列表</span>
                </div>
              </div>
              
              <!-- 知识大纲内容 -->
              <div v-if="subActiveTab === 'outline'" class="sub-panel outline-content">
                <div v-if="selectedOutline" class="selected-outline-container">
                  <div class="outline-header">
                    <button class="back-button" @click="selectedOutline = null">
                      <i class="back-icon"></i>
                      <span>返回大纲列表</span>
                    </button>
                    <h3 class="outline-title">{{ selectedOutline.title }}</h3>
                  </div>
                  <iframe :src="`/outline?embedded=true&onlyOutline=true&courseId=${selectedOutline.id}`" class="embedded-view"></iframe>
                </div>
                <MyOutlineList v-else @select-outline="handleOutlineSelect" />
              </div>
              
              <!-- 知识图谱列表内容 -->
              <div v-if="subActiveTab === 'graphview'" class="sub-panel graphview-content">
                <div v-if="selectedGraph" class="selected-graph-container">
                  <div class="graph-header">
                    <button class="back-button" @click="selectedGraph = null">
                      <i class="back-icon"></i>
                      <span>返回图谱列表</span>
                    </button>
                    <h3 class="graph-title">{{ selectedGraph.title }}</h3>
                  </div>
                  <iframe :src="`/graph?embedded=true&courseId=${selectedGraph.id}`" class="embedded-view"></iframe>
                </div>
                <MyGraphList v-else @select-graph="handleGraphSelect" />
              </div>
            </div>
            
            <!-- 课程管理面板 -->
            <div v-if="activeTab === 'courses'" class="courses-panel">
              <!-- 课程操作按钮 -->
              <div class="action-buttons">
                <button class="btn btn-primary" @click="createNewCourse">
                  <i class="btn-icon plus-icon"></i>
                  新建课程
                </button>
                <button class="btn btn-secondary" @click="importCourse">
                  <i class="btn-icon import-icon"></i>
                  导入课程
                </button>
              </div>
              
              <!-- 加载状态 -->
              <div v-if="coursesLoading" class="loading-state">
                <div class="loading-spinner"></div>
                <p class="loading-text">正在加载课程列表...</p>
              </div>

              <!-- 课程列表 -->
              <div v-else-if="courses.length > 0" class="courses-grid">
                <div
                  v-for="course in courses"
                  :key="course.id"
                  :class="['course-card', `course-card-${course.type}`]"
                  @click="enterCourse(course.id)"
                >
                  <div class="course-header">
                    <h3 class="course-title">{{ course.title }}</h3>
                    <div class="course-semester">{{ course.semester }}</div>
                  </div>
                  <div class="course-stats">
                    <div class="stat-item">
                      <span class="stat-label">学生人数</span>
                      <span class="stat-value">{{ course.studentCount }}人</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">课时</span>
                      <span class="stat-value">{{ course.totalHours }}学时</span>
                    </div>
                  </div>
                  <div class="progress-section">
                    <div class="progress-label">进度: {{ course.progress }}%</div>
                    <div class="progress-bar">
                      <div class="progress-fill" :style="`width: ${course.progress}%`"></div>
                    </div>
                  </div>

                </div>
              </div>

              <!-- 空状态 -->
              <div v-else class="empty-state">
                <div class="empty-icon">📚</div>
                <h3 class="empty-title">还没有创建任何课程</h3>
                <p class="empty-description">点击上方的"新建课程"按钮开始创建您的第一个课程</p>
                <button class="btn btn-primary" @click="createNewCourse">
                  <i class="btn-icon plus-icon"></i>
                  创建第一个课程
                </button>
              </div>
            </div>
            
            <!-- 教学助手面板 -->
            <div v-if="activeTab === 'lesson-assistant'" class="lesson-assistant-panel">
              <!-- 助手模块网格 -->
              <div class="assistant-modules-grid">
                <div class="module-card lesson-design-card" @click="handleLessonDesign">
                  <div class="module-header">
                    <div class="module-icon lesson-design-icon">
                      <i class="icon-document"></i>
                    </div>
                    <div class="module-badge">智能推荐</div>
                  </div>
                  <div class="module-content">
                    <h3 class="module-title">教案设计</h3>
                    <p class="module-desc">智能教案生成和编辑工具，支持多种教学模式</p>
                    <div class="module-stats">
                      <div class="stat-item">
                        <span class="stat-label">模板</span>
                        <span class="stat-value">50+</span>
                      </div>
                      <div class="stat-item">
                        <span class="stat-label">已创建</span>
                        <span class="stat-value">12</span>
                      </div>
                    </div>
                  </div>
                  <div class="module-footer">
                    <button class="module-btn btn-blue">开始设计</button>
                  </div>
                </div>
                
                <div class="module-card my-lessons-card" @click="handleMyLessons">
                  <div class="module-header">
                    <div class="module-icon my-lessons-icon">
                      <i class="icon-folder"></i>
                    </div>
                    <div class="module-badge recent">最近使用</div>
                  </div>
                  <div class="module-content">
                    <h3 class="module-title">我的教案</h3>
                    <p class="module-desc">查看和管理已创建的教案，支持版本管理</p>
                    <div class="module-stats">
                      <div class="stat-item">
                        <span class="stat-label">总数</span>
                        <span class="stat-value">12</span>
                      </div>
                      <div class="stat-item">
                        <span class="stat-label">本周新增</span>
                        <span class="stat-value">3</span>
                      </div>
                    </div>
                  </div>
                  <div class="module-footer">
                    <button class="module-btn btn-purple">查看教案</button>
                  </div>
                </div>
                
                <div class="module-card courseware-card" @click="handleCourseware">
                  <div class="module-header">
                    <div class="module-icon courseware-icon">
                      <i class="icon-presentation"></i>
                    </div>
                    <div class="module-badge">热门功能</div>
                  </div>
                  <div class="module-content">
                    <h3 class="module-title">课件制作</h3>
                    <p class="module-desc">多媒体课件制作和管理，支持PPT一键生成</p>
                    <div class="module-stats">
                      <div class="stat-item">
                        <span class="stat-label">课件数</span>
                        <span class="stat-value">8</span>
                      </div>
                      <div class="stat-item">
                        <span class="stat-label">模板</span>
                        <span class="stat-value">30+</span>
                      </div>
                    </div>
                  </div>
                  <div class="module-footer">
                    <button class="module-btn btn-green">制作课件</button>
                  </div>
                </div>
                
                <div class="module-card question-bank-card" @click="handleQuestionBank">
                  <div class="module-header">
                    <div class="module-icon question-bank-icon">
                      <i class="icon-quiz"></i>
                    </div>
                    <div class="module-badge">AI生成</div>
                  </div>
                  <div class="module-content">
                    <h3 class="module-title">习题库</h3>
                    <p class="module-desc">智能习题库存和组卷，AI自动生成试题</p>
                    <div class="module-stats">
                      <div class="stat-item">
                        <span class="stat-label">题目数</span>
                        <span class="stat-value">256</span>
                      </div>
                      <div class="stat-item">
                        <span class="stat-label">试卷</span>
                        <span class="stat-value">5</span>
                      </div>
                    </div>
                  </div>
                  <div class="module-footer">
                    <button class="module-btn btn-orange">题库管理</button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 班级管理面板 -->
            <div v-if="activeTab === 'student-management'" class="student-management-panel">
              <!-- 操作按钮区域 -->
              <div class="class-actions">
                <div class="actions-right">
                  <button class="btn btn-outline" @click="createNewClass">
                    <i class="btn-icon plus-icon"></i>
                    新建班级
                  </button>
                  <button class="btn btn-primary" @click="manageStudents">
                    <i class="btn-icon management-icon"></i>
                    学生管理
                  </button>
                </div>
              </div>

              <!-- 加载状态 -->
              <div v-if="classesLoading" class="loading-state">
                <div class="loading-spinner"></div>
                <p class="loading-text">正在加载班级列表...</p>
              </div>

              <!-- 班级卡片网格 -->
              <div v-else-if="classes.length > 0" class="classes-grid">
                <div
                  v-for="classItem in classes"
                  :key="classItem.id"
                  :class="['class-card', `class-card-${classItem.type}`]"
                >
                  <div class="class-header">
                    <div class="class-info">
                      <h3 class="class-title">{{ classItem.name || '未命名班级' }}</h3>
                      <div class="class-semester">{{ classItem.semester || '学期信息未设置' }}</div>
                      <div v-if="classItem.teacher" class="class-teacher">教师: {{ classItem.teacher }}</div>
                    </div>
                    <div class="class-actions">
                      <button
                        class="btn-icon edit-btn"
                        @click.stop="editClass(classItem)"
                        title="编辑班级"
                      >
                        ✏️
                      </button>
                      <button
                        class="btn-icon delete-btn"
                        @click.stop="deleteClass(classItem)"
                        title="删除班级"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                  <div class="class-stats" @click="enterClass(classItem.id)">
                    <div class="stat-item">
                      <span class="stat-label">学生人数</span>
                      <span class="stat-value">{{ classItem.studentCount || 0 }}人</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">课程</span>
                      <span class="stat-value">{{ classItem.courseCount || 0 }}门</span>
                    </div>
                  </div>
                  <div class="class-meta" @click="enterClass(classItem.id)">
                    <div class="meta-item" v-if="classItem.createTime">
                      <span class="meta-label">创建时间:</span>
                      <span class="meta-value">{{ formatDate(classItem.createTime) }}</span>
                    </div>
                    <div class="meta-item" v-if="classItem.remark">
                      <span class="meta-label">备注:</span>
                      <span class="meta-value">{{ classItem.remark }}</span>
                    </div>
                  </div>
                  <div class="progress-section" @click="enterClass(classItem.id)" v-if="classItem.avgProgress !== undefined">
                    <div class="progress-label">平均进度: {{ classItem.avgProgress || 0 }}%</div>
                    <div class="progress-bar">
                      <div class="progress-fill" :style="`width: ${classItem.avgProgress || 0}%`"></div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 空状态 -->
              <div v-else class="empty-state">
                <div class="empty-icon">🏫</div>
                <h3 class="empty-title">还没有创建任何班级</h3>
                <p class="empty-description">点击上方的"新建班级"按钮开始创建您的第一个班级</p>
                <button class="btn btn-primary" @click="createNewClass">
                  <i class="btn-icon plus-icon"></i>
                  创建第一个班级
                </button>
              </div>
            </div>
            
            <!-- 作业与讨论面板 -->
            <div v-if="activeTab === 'homework-discussion'" class="homework-discussion-panel">
              <!-- 作业与讨论模块网格 -->
              <div class="homework-modules-grid">
                <div class="module-card homework-card" @click="handleHomeworkManagement">
                  <div class="module-header">
                    <div class="module-icon homework-icon">
                      <i class="icon-assignment"></i>
                    </div>
                  </div>
                  <div class="module-content">
                    <h3 class="module-title">作业管理</h3>
                    <p class="module-desc">发布作业、批改作业、查看提交进度和成绩统计</p>
                  </div>
                  <div class="module-footer">
                    <button class="module-btn btn-blue">管理作业</button>
                  </div>
                </div>
                
                <div class="module-card discussion-card" @click="handleDiscussionManagement">
                  <div class="module-header">
                    <div class="module-icon discussion-icon">
                      <i class="icon-chat"></i>
                    </div>
                  </div>
                  <div class="module-content">
                    <h3 class="module-title">讨论管理</h3>
                    <p class="module-desc">创建讨论话题、管理学生回复、维护讨论秩序</p>
                  </div>
                  <div class="module-footer">
                    <button class="module-btn btn-green">管理讨论</button>
                  </div>
                </div>
                
                <div class="module-card qa-card" @click="handleOnlineQA">
                  <div class="module-header">
                    <div class="module-icon qa-icon">
                      <i class="icon-help"></i>
                    </div>
                  </div>
                  <div class="module-content">
                    <h3 class="module-title">在线答疑</h3>
                    <p class="module-desc">实时回答学生问题、提供学习指导和答疑服务</p>
                  </div>
                  <div class="module-footer">
                    <button class="module-btn btn-orange">开始答疑</button>
                  </div>
                </div>
                
                <div class="module-card analysis-card" @click="handleLearningAnalysis">
                  <div class="module-header">
                    <div class="module-icon analysis-icon">
                      <i class="icon-analytics"></i>
                    </div>
                  </div>
                  <div class="module-content">
                    <h3 class="module-title">学习分析</h3>
                    <p class="module-desc">分析学生学习数据、生成学习报告和改进建议</p>
                  </div>
                  <div class="module-footer">
                    <button class="module-btn btn-purple">查看分析</button>
                  </div>
                </div>
                
                <div class="module-card evaluation-card" @click="handleInteractiveEvaluation">
                  <div class="module-header">
                    <div class="module-icon evaluation-icon">
                      <i class="icon-star"></i>
                    </div>
                  </div>
                  <div class="module-content">
                    <h3 class="module-title">互动评价</h3>
                    <p class="module-desc">课堂互动评价、学生表现记录和综合评估</p>
                  </div>
                  <div class="module-footer">
                    <button class="module-btn btn-pink">开始评价</button>
                  </div>
                </div>
                
                <div class="module-card resources-card" @click="handleResourceSharing">
                  <div class="module-header">
                    <div class="module-icon resources-icon">
                      <i class="icon-share"></i>
                    </div>
                  </div>
                  <div class="module-content">
                    <h3 class="module-title">资源共享</h3>
                    <p class="module-desc">分享学习资料、课件下载和参考资源管理</p>
                  </div>
                  <div class="module-footer">
                    <button class="module-btn btn-cyan">管理资源</button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- AI助手面板 -->
            <div v-if="activeTab === 'ai-assistant'" class="ai-assistant-panel">
              <!-- AI助手模块网格 -->
              <div class="ai-modules-grid">
                <div class="module-card ai-lesson-card" @click="handleAILessonGeneration">
                  <div class="module-header">
                    <div class="module-icon ai-lesson-icon">
                      <i class="icon-ai-lesson"></i>
                    </div>
                  </div>
                  <div class="module-content">
                    <h3 class="module-title">AI生成教案</h3>
                    <p class="module-desc">智能生成个性化教案，支持多种教学模板和课程类型</p>
                  </div>
                  <div class="module-footer">
                    <button class="module-btn btn-blue">开始生成</button>
                  </div>
                </div>
                
                <div class="module-card ai-ppt-card" @click="handleAIPPTGeneration">
                  <div class="module-header">
                    <div class="module-icon ai-ppt-icon">
                      <i class="icon-ai-ppt"></i>
                    </div>
                  </div>
                  <div class="module-content">
                    <h3 class="module-title">AI生成PPT</h3>
                    <p class="module-desc">自动生成精美课件，支持多种设计风格和模板选择</p>
                  </div>
                  <div class="module-footer">
                    <button class="module-btn btn-green">开始制作</button>
                  </div>
                </div>
                
                <div class="module-card ai-chat-card" @click="handleAIChatAssistant">
                  <div class="module-header">
                    <div class="module-icon ai-chat-icon">
                      <i class="icon-ai-chat"></i>
                    </div>
                  </div>
                  <div class="module-content">
                    <h3 class="module-title">AI智能问答助手</h3>
                    <p class="module-desc">专业教学问题解答，提供教学建议和资源推荐</p>
                  </div>
                  <div class="module-footer">
                    <button class="module-btn btn-orange">开始对话</button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 资料库面板 -->
            <div v-if="activeTab === 'materials'" class="materials-panel">
              <!-- 资料库操作按钮 -->
              <div class="materials-actions">
                <div class="actions-right">
                  <button class="btn btn-outline" @click="createNewFolder">
                    <i class="btn-icon folder-icon"></i>
                    新建文件夹
                  </button>
                  <button class="btn btn-primary" @click="uploadMaterials">
                    <i class="btn-icon upload-icon"></i>
                    上传资料
                  </button>
                </div>
              </div>
  
              <!-- 资料分类网格 -->
              <div class="materials-grid">
                <div class="material-category-card course-materials-card" @click="viewCoursesMaterials">
                  <div class="category-icon">
                    📁
                  </div>
                  <div class="category-info">
                    <h3 class="category-title">课程资料</h3>
                    <p class="category-count">{{ courseMaterials.count }}个文件</p>
                  </div>
                </div>
                
                <div class="material-category-card lesson-templates-card" @click="viewLessonTemplates">
                  <div class="category-icon">
                    📄
                  </div>
                  <div class="category-info">
                    <h3 class="category-title">教案模板</h3>
                    <p class="category-count">{{ lessonTemplates.count }}个文件</p>
                  </div>
                </div>
                
                <div class="material-category-card video-resources-card" @click="viewVideoResources">
                  <div class="category-icon">
                    🎥
                  </div>
                  <div class="category-info">
                    <h3 class="category-title">视频资源</h3>
                    <p class="category-count">{{ videoResources.count }}个文件</p>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 设置面板 -->
            <div v-if="activeTab === 'settings'" class="settings-panel">
              <MySettings />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建课程对话框 -->
    <div v-if="showCreateCourseDialog" class="dialog-overlay" @click="cancelCreateCourse">
      <div class="dialog-content" @click.stop>
        <div class="dialog-header">
          <h3 class="dialog-title">创建新课程</h3>
          <button class="dialog-close" @click="cancelCreateCourse">×</button>
        </div>

        <div class="dialog-body">
          <div class="form-group">
            <label class="form-label">课程名称 *</label>
            <input
              v-model="newCourseForm.name"
              type="text"
              class="form-input"
              placeholder="请输入课程名称"
              :disabled="createCourseLoading"
            />
          </div>

          <div class="form-group">
            <label class="form-label">课程描述</label>
            <textarea
              v-model="newCourseForm.description"
              class="form-textarea"
              placeholder="请输入课程描述"
              rows="3"
              :disabled="createCourseLoading"
            ></textarea>
          </div>

          <div class="form-group">
            <label class="form-label">备注</label>
            <input
              v-model="newCourseForm.remark"
              type="text"
              class="form-input"
              placeholder="请输入备注信息"
              :disabled="createCourseLoading"
            />
          </div>
        </div>

        <div class="dialog-footer">
          <button
            class="btn btn-secondary"
            @click="cancelCreateCourse"
            :disabled="createCourseLoading"
          >
            取消
          </button>
          <button
            class="btn btn-primary"
            @click="confirmCreateCourse"
            :disabled="createCourseLoading"
          >
            <span v-if="createCourseLoading">创建中...</span>
            <span v-else>确认创建</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 创建班级对话框 -->
    <div v-if="showCreateClassDialog" class="dialog-overlay" @click="cancelCreateClass">
      <div class="dialog-content" @click.stop>
        <div class="dialog-header">
          <h3 class="dialog-title">创建新班级</h3>
          <button class="dialog-close" @click="cancelCreateClass">×</button>
        </div>

        <div class="dialog-body">
          <div class="form-group">
            <label class="form-label">班级名称 *</label>
            <input
              v-model="newClassForm.name"
              type="text"
              class="form-input"
              placeholder="请输入班级名称"
              :disabled="createClassLoading"
            />
          </div>

          <div class="form-group">
            <label class="form-label">备注</label>
            <textarea
              v-model="newClassForm.remark"
              class="form-textarea"
              placeholder="请输入备注信息"
              rows="3"
              :disabled="createClassLoading"
            ></textarea>
          </div>
        </div>

        <div class="dialog-footer">
          <button
            class="btn btn-secondary"
            @click="cancelCreateClass"
            :disabled="createClassLoading"
          >
            取消
          </button>
          <button
            class="btn btn-primary"
            @click="confirmCreateClass"
            :disabled="createClassLoading"
          >
            <span v-if="createClassLoading">创建中...</span>
            <span v-else>确认创建</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 编辑班级对话框 -->
    <div v-if="showEditClassDialog" class="dialog-overlay" @click="cancelEditClass">
      <div class="dialog-content" @click.stop>
        <div class="dialog-header">
          <h3 class="dialog-title">编辑班级</h3>
          <button class="dialog-close" @click="cancelEditClass">×</button>
        </div>

        <div class="dialog-body">
          <!-- 显示班级ID用于调试 -->
          <div class="form-group">
            <label class="form-label">班级ID</label>
            <input
              v-model="editClassForm.id"
              type="number"
              class="form-input"
              placeholder="班级ID"
              :disabled="true"
              readonly
            />
          </div>

          <div class="form-group">
            <label class="form-label">班级名称 *</label>
            <input
              v-model="editClassForm.name"
              type="text"
              class="form-input"
              placeholder="请输入班级名称"
              :disabled="editClassLoading"
            />
          </div>
        </div>

        <div class="dialog-footer">
          <button
            class="btn btn-secondary"
            @click="cancelEditClass"
            :disabled="editClassLoading"
          >
            取消
          </button>
          <button
            class="btn btn-primary"
            @click="confirmEditClass"
            :disabled="editClassLoading"
          >
            <span v-if="editClassLoading">更新中...</span>
            <span v-else>确认更新</span>
          </button>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed, onMounted } from 'vue';
  import { getCurrentUser } from '@/api/auth';
  import { getCoursesList, createCourse } from '@/api/courses';
  import { getClassesList, createClass, updateClass, deleteClasses } from '@/api/class';
  import MySettings from '@/components/my/MySettings.vue';
  // 移除个人信息组件导入
  import MyOutlineList from '@/components/my/MyOutlineList.vue';
  import MyGraphList from '@/components/my/MyGraphList.vue';
  import { useRoute, useRouter } from 'vue-router';
  
  const route = useRoute();
const router = useRouter();
  
  // 当前用户信息
  const currentUser = ref(getCurrentUser() || {});
  
  // 是否为教师角色
  const isTeacher = computed(() => {
    return currentUser.value.role === 'teacher';
  });
  
  // 当前激活的标签页 - 默认显示课程
  const activeTab = ref('courses');
  
  // 子标签页状态
  const subActiveTab = ref('outline');
  
  // 组件挂载时加载用户数据
  onMounted(async () => {
    // TODO: 从API获取最新的用户数据
    console.log('加载用户数据');

    // 检查URL参数中是否指定了要显示的标签页
    if (route.query.tab) {
      activeTab.value = route.query.tab;
    }

    // 加载课程列表
    await fetchCourses();

    // 加载班级列表
    await fetchClasses();
  });
  
  const selectedOutline = ref(null);
  const selectedGraph = ref(null);
  
  const handleOutlineSelect = (outline) => {
    selectedOutline.value = outline;
  };
  
  const handleGraphSelect = (graph) => {
    selectedGraph.value = graph;
  };
  
  // 课程数据
  const courses = ref([]);
  const coursesLoading = ref(false);

  // 获取课程列表
  const fetchCourses = async () => {
    coursesLoading.value = true;
    try {
      const response = await getCoursesList();
      if (response && response.rows && Array.isArray(response.rows)) {
        // 将API返回的课程数据映射到组件需要的格式
        courses.value = response.rows.map((course, index) => ({
          id: course.id,
          title: course.name || course.title || '未命名课程',
          semester: '2024春季学期', // API中没有学期字段，使用默认值
          studentCount: course.studentCount || 0, // API中可能没有学生数量字段
          totalHours: course.totalHours || 32, // API中可能没有总课时字段
          progress: course.progress || Math.floor(Math.random() * 100), // API中可能没有进度字段，使用随机值
          type: index % 2 === 0 ? 'blue' : 'green', // 交替使用蓝色和绿色主题
          lastModified: formatLastModified(course.updateTime || course.createTime), // 格式化最后修改时间
          description: course.description || '', // 课程描述
          deptId: course.deptId,
          teacherId: course.teacherId,
          tpId: course.tpId
        }));
        console.log('课程列表加载成功:', courses.value);
      } else {
        console.warn('课程列表数据格式不正确:', response);
        courses.value = [];
      }
    } catch (error) {
      console.error('获取课程列表失败:', error);
      courses.value = [];
    } finally {
      coursesLoading.value = false;
    }
  };

  // 格式化最后修改时间
  const formatLastModified = (dateString) => {
    if (!dateString) return '未知';

    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffHours < 1) {
      return '刚刚';
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else if (diffDays < 30) {
      const weeks = Math.floor(diffDays / 7);
      return `${weeks}周前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '未知';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      console.error('日期格式化错误:', error);
      return '日期格式错误';
    }
  };

  // 获取班级列表
  const fetchClasses = async () => {
    classesLoading.value = true;
    try {
      console.log('开始获取班级列表...');
      const response = await getClassesList();
      console.log('班级列表API响应:', response);

      if (response && response.rows && Array.isArray(response.rows)) {
        console.log('班级原始数据:', response.rows);

        // 将API返回的班级数据映射到组件需要的格式
        classes.value = response.rows.map((classItem, index) => {
          console.log(`处理班级数据 ${index + 1}:`, classItem);

          const mappedClass = {
            id: classItem.id,
            name: classItem.name || '未命名班级',
            semester: classItem.semester || '2024春季学期',
            studentCount: classItem.studentCount !== undefined ? classItem.studentCount : 0,
            courseCount: classItem.courseCount !== undefined ? classItem.courseCount : 0,
            avgProgress: classItem.avgProgress !== undefined ? classItem.avgProgress : 0,
            type: index % 2 === 0 ? 'blue' : 'green',
            teacher: classItem.teacher || classItem.createBy || currentUser.value.username || '未知教师',
            startDate: classItem.startDate || '2024-02-20',
            endDate: classItem.endDate || '2024-06-30',
            createBy: classItem.createBy,
            createTime: classItem.createTime,
            updateBy: classItem.updateBy,
            updateTime: classItem.updateTime,
            remark: classItem.remark || ''
          };

          console.log(`映射后的班级数据 ${index + 1}:`, mappedClass);
          return mappedClass;
        });

        console.log('班级列表加载成功，总数:', classes.value.length);
        console.log('最终班级数据:', classes.value);
      } else {
        console.warn('班级列表数据格式不正确:', response);
        console.log('响应类型:', typeof response);
        console.log('是否有rows属性:', response && response.hasOwnProperty('rows'));
        console.log('rows是否为数组:', response && response.rows && Array.isArray(response.rows));
        classes.value = [];
      }
    } catch (error) {
      console.error('获取班级列表失败:', error);
      classes.value = [];
    } finally {
      classesLoading.value = false;
    }
  };

  // 班级数据
  const classes = ref([]);
  const classesLoading = ref(false);

  // 资料库数据
  const courseMaterials = ref({
    count: 25,
    files: []
  });
  
  const lessonTemplates = ref({
    count: 12,
    files: []
  });
  
  const videoResources = ref({
    count: 8,
    files: []
  });
  
  // 新建课程对话框状态
  const showCreateCourseDialog = ref(false);
  const newCourseForm = ref({
    name: '',
    description: '',
    deptId: 0,
    teacherId: 0,
    tpId: 0,
    remark: ''
  });
  const createCourseLoading = ref(false);

  // 新建班级对话框状态
  const showCreateClassDialog = ref(false);
  const newClassForm = ref({
    name: '',
    createBy: '',
    createTime: '',
    updateBy: '',
    updateTime: '',
    remark: '',
    params: {},
    id: 0
  });
  const createClassLoading = ref(false);

  // 编辑班级对话框状态
  const showEditClassDialog = ref(false);
  const editClassForm = ref({
    name: '',
    createBy: '',
    createTime: '',
    updateBy: '',
    updateTime: '',
    remark: '',
    params: {},
    id: 0
  });
  const editClassLoading = ref(false);

  // 课程相关方法
  const createNewCourse = async () => {
    console.log('显示创建新课程对话框');
    // 重置表单
    newCourseForm.value = {
      name: '',
      description: '',
      deptId: 0,
      teacherId: currentUser.value.id || 0, // 使用当前用户ID作为教师ID
      tpId: 0,
      remark: ''
    };
    showCreateCourseDialog.value = true;
  };

  // 确认创建课程
  const confirmCreateCourse = async () => {
    if (!newCourseForm.value.name.trim()) {
      alert('请输入课程名称');
      return;
    }

    createCourseLoading.value = true;
    try {
      const courseData = {
        ...newCourseForm.value,
        createBy: currentUser.value.username || '',
        updateBy: currentUser.value.username || '',
        params: {}
      };

      console.log('正在创建课程:', courseData);
      const response = await createCourse(courseData);

      if (response && response.success) {
        console.log('课程创建成功');
        alert('课程创建成功！');
        showCreateCourseDialog.value = false;
        // 重新获取课程列表
        await fetchCourses();
      } else {
        console.error('课程创建失败:', response);
        alert('课程创建失败，请重试');
      }
    } catch (error) {
      console.error('创建课程时发生错误:', error);
      alert('创建课程时发生错误，请重试');
    } finally {
      createCourseLoading.value = false;
    }
  };

  // 取消创建课程
  const cancelCreateCourse = () => {
    showCreateCourseDialog.value = false;
  };

  const importCourse = async () => {
    console.log('导入课程');
    // TODO: 实现导入课程的API调用
    // 这里应该调用导入课程的API，然后重新获取课程列表
    // 暂时显示提示信息
    alert('导入课程功能正在开发中，请联系管理员');

    // 重新获取课程列表以确保数据同步
    await fetchCourses();
  };
  
  const enterCourse = (courseId) => {
    console.log('进入课程:', courseId);
    // TODO: 跳转到课程详情页
  };
  
  // 教学助手模块处理方法
  const handleLessonDesign = () => {
    console.log('开始教案设计');
    // TODO: 跳转到教案设计页面
  };
  
  const handleMyLessons = () => {
    console.log('查看我的教案');
    // TODO: 跳转到教案列表页面
  };
  
  const handleCourseware = () => {
    console.log('制作课件');
    // TODO: 跳转到课件制作页面
  };
  
  const handleQuestionBank = () => {
    console.log('题库管理');
    // TODO: 跳转到习题库页面
  };
  
  // 班级管理相关方法
  const createNewClass = async () => {
    console.log('显示创建新班级对话框');
    // 重置表单
    newClassForm.value = {
      name: '',
      createBy: currentUser.value.username || '',
      createTime: '',
      updateBy: currentUser.value.username || '',
      updateTime: '',
      remark: '',
      params: {},
      id: 0
    };
    showCreateClassDialog.value = true;
  };

  // 确认创建班级
  const confirmCreateClass = async () => {
    if (!newClassForm.value.name.trim()) {
      alert('请输入班级名称');
      return;
    }

    createClassLoading.value = true;
    try {
      const classData = {
        ...newClassForm.value,
        createBy: currentUser.value.username || '',
        updateBy: currentUser.value.username || '',
        params: {}
      };

      console.log('正在创建班级:', classData);
      const response = await createClass(classData);

      if (response && response.success) {
        console.log('班级创建成功');
        alert('班级创建成功！');
        showCreateClassDialog.value = false;
        // 重新获取班级列表
        await fetchClasses();
      } else {
        console.error('班级创建失败:', response);
        alert('班级创建失败，请重试');
      }
    } catch (error) {
      console.error('创建班级时发生错误:', error);
      alert('创建班级时发生错误，请重试');
    } finally {
      createClassLoading.value = false;
    }
  };

  // 取消创建班级
  const cancelCreateClass = () => {
    showCreateClassDialog.value = false;
  };

  // 编辑班级
  const editClass = (classItem) => {
    console.log('编辑班级:', classItem);
    // 填充表单数据，确保所有必要字段都有值
    editClassForm.value = {
      id: classItem.id || 0,
      name: classItem.name || '',
      createBy: classItem.createBy || '',
      createTime: classItem.createTime || '',
      updateBy: currentUser.value.username || '',
      updateTime: new Date().toISOString(),
      remark: classItem.remark || '',
      params: classItem.params || {}
    };
    console.log('编辑表单数据:', editClassForm.value);
    showEditClassDialog.value = true;
  };

  // 确认编辑班级
  const confirmEditClass = async () => {
    // 验证必填字段
    if (!editClassForm.value.name.trim()) {
      alert('请输入班级名称');
      return;
    }

    if (!editClassForm.value.id || editClassForm.value.id === 0) {
      alert('班级ID不能为空');
      return;
    }

    editClassLoading.value = true;
    try {
      // 构建符合API要求的数据格式
      const classData = {
        id: editClassForm.value.id,
        name: editClassForm.value.name.trim(),
        createBy: editClassForm.value.createBy || '',
        createTime: editClassForm.value.createTime || '',
        updateBy: currentUser.value.username || '',
        updateTime: new Date().toISOString(),
        remark: editClassForm.value.remark || '',
        params: editClassForm.value.params || {}
      };

      console.log('正在更新班级:', classData);
      const response = await updateClass(classData);

      if (response && response.success) {
        console.log('班级更新成功');
        alert('班级更新成功！');
        showEditClassDialog.value = false;
        // 重新获取班级列表
        await fetchClasses();
      } else {
        console.error('班级更新失败:', response);
        alert(response.msg || '班级更新失败，请重试');
      }
    } catch (error) {
      console.error('更新班级时发生错误:', error);
      alert('更新班级时发生错误，请重试');
    } finally {
      editClassLoading.value = false;
    }
  };

  // 取消编辑班级
  const cancelEditClass = () => {
    showEditClassDialog.value = false;
  };

  // 删除班级
  const deleteClass = async (classItem) => {
    console.log('删除班级:', classItem);

    // 确认删除
    const confirmDelete = confirm(`确定要删除班级"${classItem.name}"吗？此操作不可撤销。`);
    if (!confirmDelete) {
      return;
    }

    try {
      console.log('正在删除班级...', classItem.id);
      const response = await deleteClasses([classItem.id]);

      if (response && response.success) {
        console.log('班级删除成功');
        alert('班级删除成功！');
        // 重新获取班级列表
        await fetchClasses();
      } else {
        console.error('班级删除失败:', response);
        alert(response.msg || '班级删除失败，请重试');
      }
    } catch (error) {
      console.error('删除班级时发生错误:', error);
      alert('删除班级时发生错误，请重试');
    }
  };

  const manageStudents = () => {
    console.log('学生管理');
    // TODO: 跳转到学生管理页面
    alert('学生管理功能正在开发中，可以在这里查看和管理所有学生信息');
  };
  
  const enterClass = (classId) => {
    console.log('进入班级:', classId);
    // 跳转到班级详情页面
    const selectedClass = classes.value.find(c => c.id === classId);
    if (selectedClass) {
      // 使用路由跳转到班级详情页面
      router.push({
        name: 'teacher-class-detail',
        params: { id: classId }
      });
    } else {
      console.error('未找到指定的班级:', classId);
      alert('未找到指定的班级');
    }
  };
  
  // 作业与讨论模块处理方法
  const handleHomeworkManagement = () => {
    console.log('管理作业');
    // TODO: 跳转到作业管理页面
  };
  
  const handleDiscussionManagement = () => {
    console.log('管理讨论');
    // TODO: 跳转到讨论管理页面
  };
  
  const handleOnlineQA = () => {
    console.log('开始答疑');
    // TODO: 跳转到在线答疑页面
  };
  
  const handleLearningAnalysis = () => {
    console.log('查看分析');
    // TODO: 跳转到学习分析页面
  };
  
  const handleInteractiveEvaluation = () => {
    console.log('开始评价');
    // TODO: 跳转到互动评价页面
  };
  
  const handleResourceSharing = () => {
    console.log('管理资源');
    // TODO: 跳转到资源共享页面
  };
  
  // AI助手模块处理方法
  const handleAILessonGeneration = () => {
    console.log('AI生成教案');
    // TODO: 跳转到AI教案生成页面
  };
  
  const handleAIPPTGeneration = () => {
    console.log('AI生成PPT');
    // TODO: 跳转到AI PPT生成页面
  };
  
  const handleAIChatAssistant = () => {
    console.log('AI智能问答');
    // TODO: 跳转到AI问答助手页面
  };
  
  // 资料库相关方法
  const createNewFolder = () => {
    console.log('新建文件夹');
    // TODO: 显示新建文件夹对话框
  };
  
  const uploadMaterials = () => {
    console.log('上传资料');
    // TODO: 显示文件上传对话框
  };
  
  const viewCoursesMaterials = () => {
    console.log('查看课程资料');
    // TODO: 跳转到课程资料列表
  };
  
  const viewLessonTemplates = () => {
    console.log('查看教案模板');
    // TODO: 跳转到教案模板列表
  };
  
  const viewVideoResources = () => {
    console.log('查看视频资源');
    // TODO: 跳转到视频资源列表
  };
  
  
  </script>
  
  <style scoped>
  .my-page {
    min-height: 100vh;
    background-color: var(--background-color-secondary);
  }
  
  .full-width {
    max-width: 100% !important;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  
  .page-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    padding-top: 0.25rem;
  }
  
  .back-to-home-btn {
    display: flex;
    align-items: center;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    margin-right: 1rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
  }
  
  .back-to-home-btn:hover {
    background-color: var(--hover-color);
  }
  
  .back-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 0.5rem;
    position: relative;
  }
  
  .back-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 12px;
    height: 12px;
    border-left: 2px solid var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    transform: translateY(-50%) rotate(45deg);
  }
  
  .page-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
  
  .header-subtitle {
    font-size: 0.9375rem;
    color: var(--text-color-secondary);
    margin-top: 0.25rem;
  }
  
  /* 新增的左侧菜单和内容布局样式 */
  .content-layout {
    display: flex;
    gap: 0;
    min-height: 600px;
    height: calc(100vh - 60px);
  }
  
  .sidebar {
    width: 180px;
    flex-shrink: 0;
    background-color: #f5f5f5;
    border-radius: 0;
    box-shadow: none;
    padding: 0.5rem 0;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(150, 150, 150, 0.3) transparent;
  }
  
  .sidebar-menu {
    display: flex;
    flex-direction: column;
  }
  
  .menu-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: none;
    margin-bottom: 4px;
    border-radius: 0;
  }
  
  .menu-item:hover {
    background-color: #e9e9e9;
  }
  
  .menu-item.active {
    background-color: #e9e9e9;
    color: var(--primary-color);
    font-weight: 500;
    border-left: 4px solid var(--primary-color);
  }
  
  .menu-icon {
    width: 20px;
    height: 20px;
    margin-right: 0.75rem;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.7;
  }
  
  .menu-item.active .menu-icon {
    opacity: 1;
  }
  
  .menu-text {
    font-size: 0.9375rem;
    font-weight: 400;
  }
  
  .main-content {
    flex: 1;
    background-color: #ffffff;
    border-radius: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    padding: 1rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .knowledge-graph-panel, .courses-panel, .lesson-assistant-panel, .student-management-panel, .homework-discussion-panel, .ai-assistant-panel, .materials-panel, .settings-panel {
    flex: 1;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  /* 子标签导航样式 */
  .sub-tabs {
    display: flex;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 0;
    margin-bottom: 1.5rem;
  }
  
  .sub-tab-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
    font-weight: 500;
  }
  
  .sub-tab-item:hover {
    color: var(--primary-color);
    background-color: rgba(66, 153, 225, 0.05);
  }
  
  .sub-tab-item.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background-color: rgba(66, 153, 225, 0.1);
  }
  
  .sub-tab-icon {
    width: 16px;
    height: 16px;
    margin-right: 0.5rem;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.7;
  }
  
  .sub-tab-item.active .sub-tab-icon {
    opacity: 1;
  }
  
  /* 子面板样式 */
  .sub-panel {
    flex: 1;
    overflow: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(150, 150, 150, 0.3) transparent;
  }
  
  .sub-panel::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  
  .sub-panel::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .sub-panel::-webkit-scrollbar-thumb {
    background: rgba(150, 150, 150, 0.3);
    border-radius: 4px;
  }
  
  .sub-panel::-webkit-scrollbar-thumb:hover {
    background: rgba(150, 150, 150, 0.5);
  }
  
  .settings-panel {
    overflow: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(150, 150, 150, 0.3) transparent;
  }
  
  .settings-panel::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  
  .settings-panel::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .settings-panel::-webkit-scrollbar-thumb {
    background: rgba(150, 150, 150, 0.3);
    border-radius: 4px;
  }
  
  .settings-panel::-webkit-scrollbar-thumb:hover {
    background: rgba(150, 150, 150, 0.5);
  }
  
  /* 嵌入式视图 */
  .embedded-view {
    width: 100%;
    height: 95vh;
    min-height: 500px;
    border: none;
    border-radius: var(--border-radius-md);
  }
  
  .py-3 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
  
  .py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
  
  /* 菜单图标 */
  .knowledge-graph-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M19.5 5.5v13h-15v-13h15m0-2h-15c-1.1 0-2 .9-2 2v13c0 1.1.9 2 2 2h15c1.1 0 2-.9 2-2v-13c0-1.1-.9-2-2-2zM9 8h2v8H9zm4 3h2v5h-2zm-8 0h2v5H5z'/%3E%3C/svg%3E");
  }
  
  .courses-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M12 3L1 9l11 6 9-4.91V17h2V9L12 3zm0 11.13L6.08 12 12 9.87 17.92 12 12 14.13z'/%3E%3C/svg%3E");
  }
  
  .lesson-assistant-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E");
  }
  
  .student-management-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.998 1.998 0 0 0 18 7h-2c-.8 0-1.54.37-2.01 1.01l-.74 2.22-2.99-2.99C10.05 6.85 9.49 6.67 8.9 6.67c-1.66 0-3 1.34-3 3 0 .59.18 1.15.5 1.61L9 14.89V22h2v-6h2v6h4z'/%3E%3C/svg%3E");
  }
  
  .homework-discussion-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4v5l7-5h5c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H11l-4 3v-3H4V4h16v12z'/%3E%3C/svg%3E");
  }
  
  .ai-assistant-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M20 9V7c0-1.1-.9-2-2-2h-3c0-1.66-1.34-3-3-3S9 3.34 9 5H6c-1.1 0-2 .9-2 2v2c-1.66 0-3 1.34-3 3s1.34 3 3 3v4c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-4c1.66 0 3-1.34 3-3s-1.34-3-3-3zm-2 10H6V7h12v12z'/%3E%3C/svg%3E");
  }
  
  .materials-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z'/%3E%3C/svg%3E");
  }
  
  .settings-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z'/%3E%3C/svg%3E");
  }
  
  /* 子标签图标 */
  .outline-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M3 9h14V7H3v2zm0 4h14v-2H3v2zm0 4h14v-2H3v2zm16 0h2v-2h-2v2zm0-10v2h2V7h-2zm0 6h2v-2h-2v2z'/%3E%3C/svg%3E");
  }
  
  .list-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z'/%3E%3C/svg%3E");
  }
  
  /* 面板通用样式 */
  .panel-content {
    padding: 1rem 0;
  }
  
  /* 操作按钮区域 */
  .action-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    align-items: center;
  }
  
  /* 课程网格布局 */
  .courses-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-top: 1rem;
  }
  
  /* 加载状态样式 */
  .loading-state {
    text-align: center;
    padding: 4rem 2rem;
    margin-top: 1rem;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .loading-text {
    color: var(--text-color-secondary);
    font-size: 1rem;
    margin: 0;
  }

  /* 空状态样式 */
  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    margin-top: 1rem;
  }
  
  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.6;
  }
  
  .empty-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 1rem 0;
  }
  
  .empty-description {
    font-size: 1rem;
    color: var(--text-color-secondary);
    margin: 0 0 2rem 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }
  
  @media (max-width: 1200px) {
    .courses-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  
  @media (max-width: 900px) {
    .courses-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  /* 教学助手模块网格样式 */
  .assistant-modules-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .module-card {
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    border: 1px solid #f0f0f0;
    overflow: hidden;
    min-height: 280px;
  }
  
  .module-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    border-color: #d1d5db;
  }
  
  .module-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 1.5rem 1rem;
    position: relative;
  }
  
  .module-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }
  
  .lesson-design-icon {
    background: linear-gradient(135deg, #4F9CF9 0%, #3B82F6 100%);
  }
  
  .my-lessons-icon {
    background: linear-gradient(135deg, #A855F7 0%, #8B5CF6 100%);
  }
  
  .courseware-icon {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  }
  
  .question-bank-icon {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  }
  
  .module-icon i {
    font-size: 24px;
    color: white;
  }
  
  .icon-document::before {
    content: "📄";
    font-style: normal;
  }
  
  .icon-folder::before {
    content: "📁";
    font-style: normal;
  }
  
  .icon-presentation::before {
    content: "📊";
    font-style: normal;
  }
  
  .icon-quiz::before {
    content: "🎯";
    font-style: normal;
  }
  
  .module-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    background: #f3f4f6;
    color: #6b7280;
  }
  
  .module-badge.recent {
    background: #fef3c7;
    color: #d97706;
  }
  
  .module-content {
    flex: 1;
    padding: 0 1.5rem 1rem;
    text-align: left;
  }
  
  .module-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
  }
  
  .module-desc {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.5;
    margin: 0 0 1rem 0;
  }
  
  .module-stats {
    display: flex;
    gap: 1.5rem;
    margin-top: 1rem;
  }
  
  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .stat-label {
    font-size: 0.75rem;
    color: #9ca3af;
    margin-bottom: 0.25rem;
    font-weight: 500;
  }
  
  .stat-value {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1f2937;
  }
  
  .module-footer {
    padding: 1rem 1.5rem 1.5rem;
    border-top: 1px solid #f3f4f6;
    background: rgba(249, 250, 251, 0.5);
  }
  
  .module-btn {
    width: 100%;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .btn-blue {
    background: #3B82F6;
    color: white;
  }
  
  .btn-blue:hover {
    background: #2563EB;
  }
  
  .btn-purple {
    background: #8B5CF6;
    color: white;
  }
  
  .btn-purple:hover {
    background: #7C3AED;
  }
  
  .btn-green {
    background: #10B981;
    color: white;
  }
  
  .btn-green:hover {
    background: #059669;
  }
  
  .btn-orange {
    background: #F59E0B;
    color: white;
  }
  
  .btn-orange:hover {
    background: #D97706;
  }
  
  
  
  /* 班级管理样式 */
  .class-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
  }
  
  .class-actions .actions-right {
    display: flex;
    gap: 1rem;
    align-items: center;
  }

  /* 班级卡片网格 */
  .classes-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-top: 1rem;
  }

  /* 班级卡片样式 */
  .class-card {
    display: flex;
    flex-direction: column;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: #fff;
    border: 1px solid #e5e7eb;
    aspect-ratio: 16 / 9;
    cursor: pointer;
  }

  .class-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    border-color: #d1d5db;
  }

  .class-card-blue {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: #fff;
    border: none;
  }

  .class-card-green {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: #fff;
    border: none;
  }

  .class-header {
    padding: 0.75rem;
    position: relative;
    z-index: 1;
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .class-info {
    flex: 1;
  }

  .class-card-blue .class-header,
  .class-card-green .class-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
  }

  .class-actions {
    display: flex;
    gap: 0.5rem;
    margin-left: 1rem;
  }

  .edit-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 6px;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    color: inherit;
    opacity: 0.8;
  }

  .edit-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    opacity: 1;
    transform: scale(1.05);
  }

  .class-card .edit-btn {
    color: #666;
  }

  .class-card-blue .edit-btn,
  .class-card-green .edit-btn {
    color: #fff;
  }

  .delete-btn {
    background: rgba(255, 59, 48, 0.1);
    border: none;
    border-radius: 6px;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    color: #ff3b30;
    opacity: 0.8;
  }

  .delete-btn:hover {
    background: rgba(255, 59, 48, 0.2);
    opacity: 1;
    transform: scale(1.05);
  }

  .class-card .delete-btn {
    color: #ff3b30;
  }

  .class-card-blue .delete-btn,
  .class-card-green .delete-btn {
    color: #fff;
    background: rgba(255, 255, 255, 0.2);
  }

  .class-card-blue .delete-btn:hover,
  .class-card-green .delete-btn:hover {
    background: rgba(255, 59, 48, 0.3);
  }

  .class-title {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 700;
    color: inherit;
    line-height: 1.2;
  }

  .class-semester {
    font-size: 0.75rem;
    opacity: 0.9;
    margin-bottom: 0.25rem;
  }

  .class-teacher {
    font-size: 0.7rem;
    opacity: 0.8;
    font-style: italic;
  }

  .class-stats {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0.75rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  .class-card-blue .class-stats,
  .class-card-green .class-stats {
    background: rgba(255, 255, 255, 0.15);
    color: #fff;
  }

  .class-meta {
    padding: 0.5rem 0.75rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    font-size: 0.75rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
  }

  .class-card-blue .class-meta,
  .class-card-green .class-meta {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  .meta-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
  }

  .meta-item:last-child {
    margin-bottom: 0;
  }

  .meta-label {
    font-weight: 500;
    opacity: 0.8;
  }

  .meta-value {
    opacity: 0.9;
    text-align: right;
    max-width: 60%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .class-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .class-stats .stat-label {
    font-size: 0.65rem;
    opacity: 0.8;
  }

  .class-stats .stat-value {
    font-size: 0.875rem;
    font-weight: 600;
  }

  .class-card-blue .class-stats .stat-value,
  .class-card-green .class-stats .stat-value {
    color: #fff;
  }

  .class-card .progress-section {
    padding: 0.5rem 0.75rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    flex: 1;
  }

  .class-card-blue .progress-section,
  .class-card-green .progress-section {
    background: rgba(255, 255, 255, 0.15);
  }

  .class-card .progress-label {
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  .class-card-blue .progress-label,
  .class-card-green .progress-label {
    color: #fff;
    opacity: 0.9;
  }

  .class-card .progress-bar {
    height: 4px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    overflow: hidden;
  }

  .class-card-blue .progress-bar,
  .class-card-green .progress-bar {
    background-color: rgba(255, 255, 255, 0.2);
  }

  .class-card .progress-fill {
    height: 100%;
    background-color: #4299e1;
    border-radius: 2px;
    transition: width 0.3s ease;
  }

  .class-card-blue .progress-fill {
    background-color: #fff;
  }

  .class-card-green .progress-fill {
    background-color: #fff;
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .classes-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (max-width: 900px) {
    .classes-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 600px) {
    .classes-grid {
      grid-template-columns: 1fr;
    }
  }
  

  
  /* 作业与讨论模块网格样式 */
  .homework-modules-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
  
  /* 作业与讨论模块图标颜色 */
  .homework-icon {
    background: linear-gradient(135deg, #4F9CF9 0%, #3B82F6 100%);
  }
  
  .discussion-icon {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  }
  
  .qa-icon {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  }
  
  .analysis-icon {
    background: linear-gradient(135deg, #A855F7 0%, #8B5CF6 100%);
  }
  
  .evaluation-icon {
    background: linear-gradient(135deg, #EC4899 0%, #BE185D 100%);
  }
  
  .resources-icon {
    background: linear-gradient(135deg, #06B6D4 0%, #0891B2 100%);
  }
  
  /* 新增图标样式 */
  .icon-assignment::before {
    content: "📋";
    font-style: normal;
  }
  
  .icon-chat::before {
    content: "💬";
    font-style: normal;
  }
  
  .icon-help::before {
    content: "❓";
    font-style: normal;
  }
  
  .icon-analytics::before {
    content: "📊";
    font-style: normal;
  }
  
  .icon-star::before {
    content: "⭐";
    font-style: normal;
  }
  
  .icon-share::before {
    content: "📤";
    font-style: normal;
  }
  
  /* 新增按钮颜色 */
  .btn-pink {
    background: #EC4899;
    color: white;
  }
  
  .btn-pink:hover {
    background: #BE185D;
  }
  
  .btn-cyan {
    background: #06B6D4;
    color: white;
  }
  
  .btn-cyan:hover {
    background: #0891B2;
  }
  
  /* AI助手模块网格样式 */
  .ai-modules-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
  
  /* AI助手模块图标颜色 */
  .ai-lesson-icon {
    background: linear-gradient(135deg, #4F9CF9 0%, #3B82F6 100%);
  }
  
  .ai-ppt-icon {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  }
  
  .ai-chat-icon {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  }
  
  /* AI助手图标样式 */
  .icon-ai-lesson::before {
    content: "📝";
    font-style: normal;
  }
  
  .icon-ai-ppt::before {
    content: "📈";
    font-style: normal;
  }
  
  .icon-ai-chat::before {
    content: "🤖";
    font-style: normal;
  }
  
  /* 资料库样式 */
  .materials-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
  }
  
  .materials-actions .actions-right {
    display: flex;
    gap: 1rem;
    align-items: center;
  }
  
  .folder-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234a5568'%3E%3Cpath d='M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z'/%3E%3C/svg%3E");
  }
  
  .upload-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23fff'%3E%3Cpath d='M9 16h6v-6h4l-7-7-7 7h4zm-4 2h14v2H5z'/%3E%3C/svg%3E");
  }
  
  .materials-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
  
  .material-category-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
  }
  
  .material-category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    border-color: #d1d5db;
  }
  
  .category-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 10px;
    flex-shrink: 0;
  }
  
  .category-info {
    flex: 1;
  }
  
  .category-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.25rem 0;
  }
  
  .category-count {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
  }
  
  /* 响应式设计 */
  @media (max-width: 1200px) {
    .assistant-modules-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.25rem;
    }
    
    .homework-modules-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.25rem;
    }
    
    .ai-modules-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.25rem;
    }
    
    .materials-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.25rem;
    }
  }
  
  @media (max-width: 768px) {
    .assistant-modules-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    .homework-modules-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    .ai-modules-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    .materials-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    .materials-actions {
      justify-content: flex-start;
    }
    
    .materials-actions .actions-right {
      width: 100%;
      justify-content: flex-start;
      flex-direction: column;
      align-items: stretch;
      gap: 0.75rem;
    }
    
    .materials-actions .btn {
      width: 100%;
    }
    
    .material-category-card {
      padding: 1.25rem;
    }
    
    .category-icon {
      font-size: 2rem;
      width: 50px;
      height: 50px;
    }
    
    .module-card {
      min-height: 240px;
    }
    
    .module-header {
      padding: 1.25rem 1.25rem 0.75rem;
    }
    
    .module-content {
      padding: 0 1.25rem 0.75rem;
    }
    
    .module-footer {
      padding: 0.75rem 1.25rem 1.25rem;
    }
    
    .module-icon {
      width: 50px;
      height: 50px;
    }
    
    .module-icon i {
      font-size: 20px;
    }
    
        /* 班级管理响应式 */
    .class-actions {
      justify-content: flex-start;
    }
    
    .class-actions .actions-right {
      width: 100%;
      justify-content: flex-start;
    }
  }
  
  @media (max-width: 600px) {
    .courses-grid {
      grid-template-columns: 1fr;
    }
    
    .action-buttons {
      flex-direction: column;
      align-items: stretch;
    }
    
    .empty-state {
      padding: 3rem 1rem;
    }
    
    .empty-icon {
      font-size: 3rem;
    }
    
    .empty-title {
      font-size: 1.25rem;
    }
  }
  
  .sidebar::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  
  .sidebar::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .sidebar::-webkit-scrollbar-thumb {
    background: rgba(150, 150, 150, 0.3);
    border-radius: 4px;
  }
  
  .sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(150, 150, 150, 0.5);
  }
  
  @media (max-width: 768px) {
    .content-layout {
      flex-direction: column;
      height: auto;
    }
    
    .sidebar {
      width: 100%;
      margin-bottom: 1.5rem;
    }
  }
  
  @media (min-width: 1200px) {
    .container.full-width {
      padding-left: 2rem;
      padding-right: 2rem;
    }
    
    .content-layout {
      gap: 2rem;
    }
  }
  
  @media (min-width: 1600px) {
    .container.full-width {
      padding-left: 1rem;
      padding-right: 1rem;
    }
    
    .sidebar {
      width: 200px;
    }
    
    .content-layout {
      height: calc(100vh - 50px);
    }
    
    .embedded-view {
      height: 98vh;
    }
  }
  
  /* 大纲相关样式 */
  .selected-outline-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .outline-header {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background-color: var(--background-color);
  }
  
  .back-button {
    display: flex;
    align-items: center;
    background: none;
    border: none;
    color: #007AFF;
    font-size: 0.9375rem;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .back-button:hover {
    background-color: rgba(0, 122, 255, 0.1);
  }
  
  .back-button .back-icon {
    margin-right: 0.375rem;
    width: 16px;
    height: 16px;
    position: relative;
  }
  
  .back-button .back-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 10px;
    height: 10px;
    border-left: 2px solid #007AFF;
    border-bottom: 2px solid #007AFF;
    transform: translateY(-50%) rotate(45deg);
  }
  
  .outline-title {
    margin: 0 0 0 1rem;
    font-size: 1.125rem;
    font-weight: 500;
  }
  
  /* 暗色模式适配 */
  @media (prefers-color-scheme: dark) {
    .outline-header {
      border-bottom-color: rgba(255, 255, 255, 0.1);
    }
  }
  
  /* 知识图谱列表相关样式 */
  .selected-graph-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .graph-header {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background-color: var(--background-color);
  }
  
  .back-button {
    display: flex;
    align-items: center;
    background: none;
    border: none;
    color: #007AFF;
    font-size: 0.9375rem;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .back-button:hover {
    background-color: rgba(0, 122, 255, 0.1);
  }
  
  .back-button .back-icon {
    margin-right: 0.375rem;
    width: 16px;
    height: 16px;
    position: relative;
  }
  
  .back-button .back-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 10px;
    height: 10px;
    border-left: 2px solid #007AFF;
    border-bottom: 2px solid #007AFF;
    transform: translateY(-50%) rotate(45deg);
  }
  
  .graph-title {
    margin: 0 0 0 1rem;
    font-size: 1.125rem;
    font-weight: 500;
  }
  
  /* 暗色模式适配 */
  @media (prefers-color-scheme: dark) {
    .graph-header {
      border-bottom-color: rgba(255, 255, 255, 0.1);
    }
  }
  
  /* 课程卡片样式 */
  .course-card {
    display: flex;
    flex-direction: column;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: #fff;
    border: 1px solid #e5e7eb;
    aspect-ratio: 16 / 9;
    cursor: pointer;
  }
  
  .course-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    border-color: #d1d5db;
  }
  
  .course-card-blue {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: #fff;
    border: none;
  }
  
  .course-card-green {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: #fff;
    border: none;
  }
  
  .course-header {
    padding: 0.75rem;
    position: relative;
    z-index: 1;
    flex: 1;
  }
  
  .course-card-blue .course-header,
  .course-card-green .course-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
  }
  
  .course-title {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 700;
    color: inherit;
    line-height: 1.2;
  }
  
  .course-semester {
    font-size: 0.75rem;
    opacity: 0.9;
  }
  
  .course-stats {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0.75rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }
  
  .course-card-blue .course-stats,
  .course-card-green .course-stats {
    background: rgba(255, 255, 255, 0.15);
    color: #fff;
  }
  
  .stat-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }
  
  .stat-label {
    font-size: 0.65rem;
    opacity: 0.8;
  }
  
  .stat-value {
    font-size: 0.875rem;
    font-weight: 600;
  }
  
  .course-card-blue .stat-value,
  .course-card-green .stat-value {
    color: #fff;
  }
  
  .progress-section {
    padding: 0.5rem 0.75rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    flex: 1;
  }
  
  .course-card-blue .progress-section,
  .course-card-green .progress-section {
    background: rgba(255, 255, 255, 0.15);
  }
  
  .progress-label {
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }
  
  .course-card-blue .progress-label,
  .course-card-green .progress-label {
    color: #fff;
    opacity: 0.9;
  }
  
  .progress-bar {
    height: 4px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    overflow: hidden;
  }
  
  .course-card-blue .progress-bar,
  .course-card-green .progress-bar {
    background-color: rgba(255, 255, 255, 0.2);
  }
  
  .progress-fill {
    height: 100%;
    background-color: #4299e1;
    border-radius: 2px;
    transition: width 0.3s ease;
  }
  
  .course-card-blue .progress-fill {
    background-color: #fff;
  }
  
  .course-card-green .progress-fill {
    background-color: #fff;
  }
  
  
  
  .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    white-space: nowrap;
    text-transform: none;
    letter-spacing: 0.025em;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  
  .btn-primary {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: #fff;
    box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
  }
  
  .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(66, 153, 225, 0.4);
  }
  
  .course-card-blue .btn-primary,
  .course-card-green .btn-primary {
    background: rgba(255, 255, 255, 0.9);
    color: #1a202c;
    backdrop-filter: blur(10px);
  }
  
  .course-card-blue .btn-primary:hover,
  .course-card-green .btn-primary:hover {
    background: #fff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  .btn-secondary {
    background-color: #f7fafc;
    color: #4a5568;
    border: 1px solid #e2e8f0;
  }
  
  .btn-secondary:hover {
    background-color: #edf2f7;
    border-color: #cbd5e0;
    transform: translateY(-1px);
  }
  
  .btn-outline {
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.5);
    color: inherit;
  }
  
  .course-card-blue .btn-outline,
  .course-card-green .btn-outline {
    border-color: rgba(255, 255, 255, 0.6);
    color: #fff;
  }
  
  .btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.8);
    transform: translateY(-1px);
  }
  
  .course-card-blue .btn-outline:hover,
  .course-card-green .btn-outline:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: #fff;
  }
  
  .btn-small {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }
  
  .btn-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 0.5rem;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    vertical-align: middle;
  }
  
  .plus-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23fff'%3E%3Cpath d='M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z'/%3E%3C/svg%3E");
  }
  
  .import-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236c757d'%3E%3Cpath d='M9 16h6v-6h4l-7-7-7 7h4zm-4 2h14v2H5z'/%3E%3C/svg%3E");
  }

  .management-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23fff'%3E%3Cpath d='M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.998 1.998 0 0 0 18 7h-2c-.8 0-1.54.37-2.01 1.01l-.74 2.22-2.99-2.99C10.05 6.85 9.49 6.67 8.9 6.67c-1.66 0-3 1.34-3 3 0 .59.18 1.15.5 1.61L9 14.89V22h2v-6h2v6h4z'/%3E%3C/svg%3E");
  }
  
  /* 响应式优化 */
  @media (max-width: 1200px) {
    .sidebar {
      width: 160px;
    }
    
    .menu-text {
      font-size: 0.875rem;
    }
  }
  
  @media (max-width: 992px) {
    .content-layout {
      flex-direction: column;
      height: auto;
    }
    
    .sidebar {
      width: 100%;
      margin-bottom: 1rem;
      flex-direction: row;
      overflow-x: auto;
      padding: 0.5rem;
    }
    
    .sidebar-menu {
      flex-direction: row;
      gap: 0.5rem;
      min-width: max-content;
    }
    
    .menu-item {
      white-space: nowrap;
      padding: 0.625rem 1rem;
      border-radius: var(--border-radius-md);
      margin-bottom: 0;
      border-left: none;
    }
    
    .menu-item.active {
      border-left: none;
      background-color: var(--primary-color);
      color: #fff;
    }
    
    .main-content {
      padding: 1.5rem;
    }
  }
  
  @media (max-width: 576px) {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
    
    .panel-title {
      font-size: 1.5rem;
    }
    
    .courses-grid {
      grid-template-columns: 1fr;
    }
    
    .course-stats {
      flex-direction: column;
      gap: 0.5rem;
    }
    
    .course-actions {
      flex-direction: column;
      gap: 0.5rem;
    }
  }

  /* 对话框样式 */
  .dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .dialog-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
  }

  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .dialog-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
  }

  .dialog-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6b7280;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dialog-close:hover {
    color: #374151;
  }

  .dialog-body {
    padding: 1.5rem;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  .form-input,
  .form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    transition: border-color 0.2s;
    box-sizing: border-box;
  }

  .form-input:focus,
  .form-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .form-input:disabled,
  .form-textarea:disabled {
    background-color: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
  }

  .form-textarea {
    resize: vertical;
    min-height: 80px;
  }

  .dialog-footer {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
    padding: 1.5rem;
    border-top: 1px solid #e5e7eb;
  }

  .dialog-footer .btn {
    min-width: 80px;
  }

  @media (max-width: 640px) {
    .dialog-content {
      width: 95%;
      margin: 1rem;
    }

    .dialog-header,
    .dialog-body,
    .dialog-footer {
      padding: 1rem;
    }

    .dialog-footer {
      flex-direction: column;
    }

    .dialog-footer .btn {
      width: 100%;
    }
  }
  </style>